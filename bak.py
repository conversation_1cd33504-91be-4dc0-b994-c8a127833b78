import requests
import random
from gmssl import sm2
import json
import json
from PIL import Image, ImageDraw, ImageFont
import datetime
import base64
import re
from io import BytesIO
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import binascii

def random_proxy():
    # 定义代理地址列表
    hosts = [
    '*************:7890',
    '************:7890',
    '***********:7890'
    ]

    # 随机选择一个代理地址
    host = random.choice(hosts)

    # 配置代理
    proxy = {
        'http': f'http://{host}',  # 如果代理需要用户名和密码，可以按此格式 'http://username:password@{host}'
        'https': f'http://{host}'
    }
    return proxy


# 读取身份证前六位与出生地的映射关系
with open('./csd.json', 'r', encoding='utf-8') as file:
    location_data = json.load(file)

def get_birthplace(idcard: str) -> str:
    # 提取身份证前六位
    location_code = idcard[:6]
    # 查找对应的出生地
    return location_data.get(location_code, '空')

def get_birthday_and_age(idcard: str):
    """从身份证号码解析生日和年龄"""
    birthday = idcard[6:14]
    current_year = datetime.datetime.now().year
    birth_year = int(birthday[:4])
    age = current_year - birth_year
    return f'{birthday[0:4]}年{birthday[4:6]}月{birthday[6:8]}日'





def get_gender_from_id(idcard):
    """
    根据身份证号码判断性别
    :param idcard: 身份证号码（字符串）
    :return: "男" 或 "女"
    """
    if len(idcard) != 18 or not idcard[:-1].isdigit():
        return "身份证号码格式错误"
    
    gender_digit = int(idcard[16])  # 第 17 位（索引从 0 开始）
    return "男" if gender_digit % 2 == 1 else "女"

# 绘制文本
def draw_text(draw, text_address, text_lines, canvas_width, canvas_height, font, fontSize, color='black'):
    lines = []
    max_width = 650  # 可以根据需要调整每行的最大宽度  # 人像尺寸 358*441
    num = 0
    # 将超出画布的文字分割
    while (num < len(text_lines)):
        word = text_lines[num]
        # print(draw.textlength(word, font=font))
        # print(max_width)
        if draw.textlength(word, font=font) <= max_width:
            lines.append(word)
            num = num + 1
        else:
            index = 0
            for i in range(0, len(word)):
                if draw.textlength(word[0:i+1], font=font) > max_width:
                    index = i
                    break
            lines.append(word[0:index])
            text_lines[num] = word[index:]
            # num = num - 1

    # 在画布上绘制多行文本
    text = '\n'.join(lines) # 设置打印的文本内容
    # 获取文本高度: 字号大小 * 行数
    # text_height = fontSize * len(lines)

    # 绘制文本 - 填充黑; 多行居左对齐; 行间距为 10
    draw.text(text_address, text, font=font, fill="black", spacing=10, align ="left")

# 成比例缩放图像
def resize_image_in_scale(image, scale_size):
    # 获取原始图像的宽度和高度
    original_width, original_height = image.size

    # 计算新的宽度和高度
    new_width = int(original_width * float(scale_size))
    new_height = int(original_height * float(scale_size))

    # 等比例放大图像
    resized_image = image.resize((new_width, new_height))
    return resized_image

# AES-ECB加密函数
def encrypt_aes_ecb(plaintext):
    key = 'MfyfzF7Ahyud1r12'.encode(encoding='utf-8')  # 已知的密钥
    cipher = AES.new(key, AES.MODE_ECB)
    ciphertext_bytes = cipher.encrypt(pad(plaintext.encode(), AES.block_size))
    ciphertext_hex = binascii.hexlify(ciphertext_bytes).decode()
    return ciphertext_hex

def generate_document(name, idcard, image_base64=""):
    todaytime = datetime.datetime.now().strftime("%Y-%m-%d")  # 仅保留年月日

    # 解码 base64 图片数据并转换为图像
    base64_image = image_base64  # 假设 JSON 中的键为 'image'
    image_data = base64.b64decode(base64_image)
    image = Image.open(BytesIO(image_data))
    gender = get_gender_from_id(idcard)
    cardId = idcard
    nation = f"汉"
    censusAddress = get_birthplace(idcard)
    birthday = get_birthday_and_age(idcard)

    # 打开图片
    templete_path = 'plc.jpg'
    templete = Image.open(templete_path)
    image = resize_image_in_scale(image, 1.4)
    # 将图片放置在底布的最左侧
    # 将要添加的图片粘贴到背景图片上
    templete.paste(image, (21, 350), mask=image.split()[3] if image.mode == 'RGBA' else None)
    templete_width = 1280  # 替换为你想要的画布宽度
    templete_height = 980  # 替换为你想要的画布高度
    
    # 选择字体和字号
    font = ImageFont.truetype("heiti.ttf", 55)    # 替换为你想要的字体和字号
    # 创建绘图对象
    draw = ImageDraw.Draw(templete)

    # 设置文字的位置

    # 设置文字的颜色
    text_color = (0, 0, 0)  # 黑色

    # 在指定位置添加文字 - 姓名
    # 姓名
    draw_text(draw, (683, 339), [name], templete_width, templete_height, font, fontSize=55)
    # 性别
    draw_text(draw, (684, 428), [gender], templete_width, templete_height, font, fontSize=55)
    # 民族
    draw_text(draw, (904, 428), [nation], templete_width, templete_height, font, fontSize=55)
    # 出生日期
    draw_text(draw, (810, 521), [birthday], templete_width, templete_height, font, fontSize=55)
    # 证件号码
    draw_text(draw, (564, 676), [cardId], templete_width, templete_height, font, fontSize=55)
    # 户口地址
    draw_text(draw, (560, 803), [censusAddress], templete_width, templete_height, font, fontSize=55)
    # 时间戳
    font = ImageFont.truetype("heiti.ttf", 20)    # 替换为你想要的字体和字号
    # draw_text(draw, (5, 5), [encrypt_aes_ecb(todaytime)], templete_width, templete_height, font, fontSize=20)     
    buffer = BytesIO()
    templete.save(buffer, format="JPEG")
    return base64.b64encode(buffer.getvalue()).decode("utf-8")





# 创建一个UserAgent对象
# 生成伪随机 Chrome Header
def generate_random_chrome_ua():
    random_int = random.randint(95, 119)
    random_int2 = random.randint(520, 537)
    random_int3 = random.randint(10, 40)
    user_agents = f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{random_int2}.{random_int3} (KHTML, like Gecko) Chrome/{random_int}.0.0.0 Safari/{random_int2}.{random_int3}"
    return user_agents



def checkTokenAlived(token):
    api = "https://gdrk.gdga.gd.gov.cn:8080/service/yjm/api-pi/queryOccupancy/pageReq"
    newHeaders = {
        "User-Agent": generate_random_chrome_ua(),
        "Referer": "https://gdrk.gdga.gd.gov.cn:8080/",
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "xm": "",
        "order": "djsj",
        "sort": "desc",
        "lxdh": "",
        "dzmc": "",
        "ywhj": "so",
        "begin": "",
        "end": "",
        "pageIndex": 1,
        "pageSize": 10
    }
    response = requests.post(url=api, headers=newHeaders, json=data, proxies=random_proxy(), timeout=60)
    if int(response.json()["code"]) == 200: # 只有请求响应返回 json 且 json 中 "code" 值为 200 才代表 Token 有效
        return True
    else: 
        return False

def getTheToken():
    status = False
    counter = 0
    token = ""
    while (status == False):
        readTokenApi = "https://managejlsbsession.mihoyo.cn.com/readGDToken?secret_key=ZoGnAffh0dy4MpCpvG8D"
        readTokenResponse = requests.get(url=readTokenApi)
        token = readTokenResponse.json()["GDToken"]
        if checkTokenAlived(token) != True:
            updateTokenApi = "https://dlix55vgrv2urmpj4dwfco5ali0fsbjc.lambda-url.ap-east-1.on.aws/gdYjmLogin?secret_key=RqsKjh0KmYYG5172Q7PH"
            updateTokenResponse = requests.get(url=updateTokenApi)
            if counter > 3:
                raise Exception("Too many times retry when updating token.")
            if updateTokenResponse.ok != True:
                raise Exception("Error happened when updating token.")
            else:
                counter = counter + 1
        else:
            status = True
    return token


# 粤居码某些api请求传参为加密参数，需要进行sm2国密加密
def encryptYjmWebRequestData(data):
    sm2PubKey = "04f0fb997d5658b34cd9c54768c9f1023e3cab391f9faa68c8fb3f63799c54ad399c8322d86d0f4e0f9cc2f3a0483071975275188cddf05a00e4cbbcde7b7f8229"
    # 创建 SM2 密钥对 
    # sm2.CryptSM2() 构造函数中 mode 参数需要手动等于1 (C1C3C2 模式)（默认为0: C1C2C3 模式）
    sm2_crypt = sm2.CryptSM2(public_key=sm2PubKey, private_key='', mode=1) # 16 进制的公钥
    # 使用公钥进行加密
    ciphertext = sm2_crypt.encrypt(data.encode('utf-8'))
    # 使用 hex() 函数将字节数据转换为十六进制字符串
    hex_string = ciphertext.hex()
    # print(f"加密结果: {hex_string}\n")
    return hex_string

def gdPhoto(token, cardId):
    api = "https://gdrk.gdga.gd.gov.cn:8080/service/yjm/api-pi/image"
    headers = {
        "Authorization": f"Bearer {token}",
        "User-Agent": generate_random_chrome_ua(),
        "Content-Type": 'application/json;charset=UTF-8',
        "Accept": "application/json, text/plain, */*",
        "Origin": "https://gdrk.gdga.gd.gov.cn:8080",
        "Referer": "https://gdrk.gdga.gd.gov.cn:8080/",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "en-US,en;q=0.9"
    }
    data = {
        "cyzjdm": "111",    # 证件类型 （居民身份证）
        # "cyzjdm": "414",    # 证件类型 （护照）
        "gjhdqdm": "CHN",   # 国家和地区代码
        "zjhm": str(cardId)
    }
    encrypedData = {
        "cipherData": encryptYjmWebRequestData(json.dumps(data))
    }
    response = requests.post(url=api, headers=headers, json=encrypedData, proxies=random_proxy(), timeout=60)
    # print(response.text)
    if response.ok != True:
        raise Exception("Error happened when getImage.")
    return response.json()['result']

# Telegram 相关配置
TELEGRAM_BOT_TOKEN = "**********************************************"


def send_file_to_telegram(file_path, chatid):
    """
    Send an XLS file to a Telegram bot chat.
    
    :param file_path: Path to the XLS file.
    """
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendPhoto"
    with open(file_path, 'rb') as file:
        files = {"photo": file}
        data = {
            "chat_id": chatid,
            'caption': file_path[5:23]  # 文件的注释
        }
        response = requests.post(url, data=data, files=files)
        if response.status_code != 200:
            raise Exception(f"Failed to send file to Telegram: {response.text}")

# 放缩图片，小于358*441的都放大到358*441
def resize_image(image_base64: str, target_width: int = 358, target_height: int = 441) -> str:
    # 解码Base64字符串为图像数据
    img_data = base64.b64decode(image_base64)
    img = Image.open(BytesIO(img_data))

    # 获取当前图像的宽高
    width, height = img.size

    # 如果图像尺寸小于目标尺寸，则强制缩放
    if width != target_width or height != target_height:
        img = img.resize((target_width, target_height))

    # 将处理后的图像保存为内存中的字节流
    buffered = BytesIO()
    img.save(buffered, format="JPEG")
    buffered.seek(0)

    # 将字节流转换为Base64
    img_base64_output = base64.b64encode(buffered.read()).decode("utf-8")

    return img_base64_output

def remove_mark_and_save(image_base64_input, idcard, chatid, name):
    # API URL 和请求头
    url = "https://platform.dewatermark.ai/api/object_removal/v1/erase_watermark"
    headers = {
        "X-API-KEY": "c5140cbf3a8ffc57656d39378ac960d6edd6a22957c6992f5832e73be66471e7",  # 替换为你的API密钥
    }
    # 放缩图片，小于358*441的都放大到358*441
    image_base64 = resize_image(image_base64_input, 358, 441)
    # 将 Base64 字符串解码为二进制数据
    image_binary = base64.b64decode(image_base64)

    # 要上传的数据
    files = {
        'original_preview_image': image_binary,
    }


    # 发送POST请求
    response = requests.post(url, headers=headers, files=files)

    # 处理响应
    if response.status_code == 200:
        # 获取响应中的 Base64 图片
        response_json = response.json()
        base64_image = response_json.get("edited_image", {}).get("image", "")

        if base64_image:
            idcard = f"{idcard}"
            # 调用方法
            image_data_b64 = generate_document(name, idcard, base64_image)
            image_data = base64.b64decode(image_data_b64)
            # 保存解码后的图片到本地
            output_file_path = f"/tmp/{idcard}.jpg"  # 指定保存的文件路径
            with open(output_file_path, "wb") as output_image_file:
                output_image_file.write(image_data)
            send_file_to_telegram(output_file_path, chatid)
            return True
        else:
            raise Exception("未找到 Base64 图片数据")
    else:
        raise Exception(f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}")

def addcredit(userid):
    readcreditApi = f"https://managejlsbsession.mihoyo.cn.com/readcredit?secret_key=ZoGnAffh0dy4MpCpvG8D&userid={userid}"
    readcreditResponse = requests.get(url=readcreditApi)
    credit = int(readcreditResponse.json()["credit"])
    credit = credit+2
    saveNewCreditapi = f"https://managejlsbsession.mihoyo.cn.com/updatecredit?secret_key=ZoGnAffh0dy4MpCpvG8D&userid={userid}"
    saveNewTokenResponse = requests.post(url=saveNewCreditapi, data=str(credit))
    return saveNewTokenResponse.ok

def add_double_backslash(text):
    result = ''
    is_in_square_brackets = False

    for char in text:
        if char == '[':
            is_in_square_brackets = True
        elif char == ']':
            is_in_square_brackets = False
        elif char == '_' and not is_in_square_brackets:
            result += '\\'

        result += char

    return result

# 发送文字消息，自动拆分长文本
def send_text_message(chat_id, text, parse_mode):
    TELEGRAM_MAX_LENGTH = 3000
    # Markdown 模式转义
    if parse_mode == 'Markdown':
        text = add_double_backslash(text)

    # 拆分为多段，每段不超过 Telegram 限制
    parts = [text[i:i + TELEGRAM_MAX_LENGTH] for i in range(0, len(text), TELEGRAM_MAX_LENGTH)]

    for i, part in enumerate(parts):
        payload = {
            'chat_id': str(chat_id),
            'text': part,
            'parse_mode': parse_mode
        }

        response = requests.post(
            f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(payload)
        )
        data = response.json()

        # 如果出错，递归通知调试账号
        if not data.get('ok', True):
            debug_msg = f"#DebugMessage #sendTextMessage (part {i+1}):\n{json.dumps(data)}\n\nuserTGId: {chat_id}"
            if chat_id != 6564949431:
                send_text_message(6564949431, debug_msg, '')

def check_image_size(base64_string, width=358, height=441):
    # 解码 base64 字符串
    image_data = base64.b64decode(base64_string)
    
    # 使用PIL从二进制数据加载图片
    image = Image.open(BytesIO(image_data))
    
    # 获取图片的尺寸
    img_width, img_height = image.size
    
    # 判断是否符合尺寸要求
    if img_width >= width and img_height >= height:
        return True
    else:
        return False
    

def check_string_in_file(input_string:str):
    """
    检查输入的字符串是否存在于文件中，忽略大小写
    :param input_string: 用户输入的字符串
    :param file_path: 本地文件路径
    :return: 存在时返回 "Found: <input_string>", 否则返回 "Not Found"
    """
    try:
        with open("./mgk.txt", 'r', encoding='utf-8') as file:
            # 读取所有行并去除换行符，转换为小写
            file_lines = {line.strip().upper() for line in file}

        # 检查字符串是否在文件中，转换输入字符串为小写
        if input_string.upper() in file_lines:
            return True
        else:
            return False

    except FileNotFoundError:
        raise Exception("Error: File not found")
    except Exception as e:
        raise Exception(f"Error: {str(e)}")
    
def lambda_handler(event, context):
    if event['rawPath'] == '/gdPhoto2':
        # 初始化查询参数
        inputParams = {
            'name': event['queryStringParameters'].get('name', ''),
            'cardId': event['queryStringParameters'].get('cardId', None),
            'chatid': event['queryStringParameters'].get('chatid', None),
            'secret_key': event['queryStringParameters'].get('secret_key', None)
        }
        # 初始化请求参数
        result = None
        name = inputParams.get('name', '')
        cardId = str(inputParams.get('cardId'))
        chatid = inputParams.get('chatid')
        secret_key = inputParams.get('secret_key')
        
        # 判断参数是否完整，完整则进入下一步
        if ((cardId != None) and (chatid != None) and (secret_key == "CKYJTRrkrpoWjAXi3BuF")):
            message = '0'
            ifmg = True
            ifmg = check_string_in_file(cardId)
            if ifmg == True:
                message = f'{cardId} 敏感不给查。'
                send_text_message(chatid, f'{message}', '')
            else:             
                try:
                    token = getTheToken()
                    rawResult = gdPhoto(token, cardId.upper())
                    if rawResult['certificateImage'] is None:
                        message = f'{name} {cardId} 空'
                        send_text_message(chatid, f'{message}', '')
                    elif check_image_size(rawResult['certificateImage']):
                        try:
                            remove_mark_and_save(rawResult['certificateImage'], cardId, chatid, name)
                            if addcredit(chatid) == True:
                                message = f'{cardId} 照片已发送。使用次数+1'
                                #send_text_message(chatid, f'{message}', '')
                            else:
                                message = f'{cardId} 照片已发送。使用次数未增加'
                                #send_text_message(chatid, f'{message}', '')

                        except Exception as error:
                            errormessage = f'{name} {cardId} 加工失败，请联系管理员。'
                            send_text_message(chatid, f'{errormessage}', '')
                    else:
                        message = f'{name} {cardId} 照片尺寸过小，报空。'
                        send_text_message(chatid, f'{message}', '')
                    '''
                    else:
                        try:
                            remove_mark_and_save(rawResult['certificateImage'], cardId, chatid, name)
                            if addcredit(chatid) == True:
                                message = f'{cardId} 照片已发送。使用次数+1'
                                #send_text_message(chatid, f'{message}', '')
                            else:
                                message = f'{cardId} 照片已发送。使用次数未增加'
                                #send_text_message(chatid, f'{message}', '')

                        except Exception as error:
                            errormessage = f'{name} {cardId} 加工失败，请联系管理员。'
                            send_text_message(chatid, f'{errormessage}', '')
                    '''
                     
                except Exception as error:
                        message = f'{name} {cardId} 网络波动。请重试！'
                        send_text_message(chatid, f'{message}', '')
                        raise Exception(error)

            result = {
                "code": 200,
                "message": message
            }
        else:
            result = {'message': 'Parameters are not enough.'}
        response = {
            'statusCode': 200,
            'headers': {'Content-Type': 'application/json'},
            'body': result
        }

        # logSender(json.dumps(logMessage, ensure_ascii=False, indent=4))
        return response
    else:
        return {
            'statusCode': 404
        } # 非指定路径统一返回 404
        
# 测试代码
'''
event = {
    "rawPath": "/gdPhoto2",
    "queryStringParameters": {
        "name": "测试",
        "cardId": "342501199507232623",
        "chatid": "6564949431",
        "secret_key": "CKYJTRrkrpoWjAXi3BuF"
    }
}
print(lambda_handler(event=event, context={}))
'''


