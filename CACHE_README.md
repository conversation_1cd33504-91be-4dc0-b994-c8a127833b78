# AWS Lambda 缓存功能说明

## 概述

为了提升 AWS Lambda 函数的运行速度，我们为 `random_proxy()` 和 `getTheToken()` 函数添加了缓存机制。这可以显著减少网络请求次数，提高响应速度。

## 缓存配置

### 代理缓存 (random_proxy)
- **缓存时间**: 5分钟 (300秒)
- **缓存内容**: 代理服务器列表
- **缓存策略**: 
  - 首次调用时从API获取代理列表并缓存
  - 5分钟内的后续调用直接使用缓存
  - 缓存过期后重新获取
  - 如果API请求失败，会尝试使用过期缓存

### Token缓存 (getTheToken)
- **缓存时间**: 30分钟 (1800秒)
- **缓存内容**: 有效的访问Token
- **缓存策略**:
  - 首次调用时获取Token并验证有效性
  - 30分钟内的后续调用先检查缓存Token是否仍然有效
  - 如果缓存Token失效，会重新获取新Token
  - 缓存过期后重新获取

## 性能提升

### 预期性能改善
- **代理获取**: 从网络请求(~1-3秒) 降低到内存读取(~0.001秒)
- **Token获取**: 从完整验证流程(~2-5秒) 降低到缓存读取(~0.001秒)
- **总体响应时间**: 在缓存命中的情况下，可减少 80-95% 的响应时间

### 缓存命中率
- 在高频使用场景下，缓存命中率可达到 90% 以上
- Lambda 容器复用时，缓存在多次调用间保持有效

## 新增API接口

### 1. 查看缓存状态
```
GET /cache-status?secret_key=CKYJTRrkrpoWjAXi3BuF
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Cache status retrieved successfully",
  "data": {
    "proxy_cache": {
      "valid": true,
      "remaining_seconds": 245.3,
      "data_count": 15
    },
    "token_cache": {
      "valid": true,
      "remaining_seconds": 1654.7,
      "has_token": true
    }
  }
}
```

### 2. 清理缓存
```
GET /clear-cache?secret_key=CKYJTRrkrpoWjAXi3BuF
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Cache cleared successfully"
}
```

## 日志输出

缓存系统会输出详细的日志信息，帮助监控缓存使用情况：

```
[INFO] 使用代理缓存，剩余时间: 245.3秒
[INFO] 代理缓存过期，重新获取代理列表
[INFO] 代理缓存已更新，获取到 15 个代理
[INFO] 选择代理: 1.2.3.4:1080
[INFO] 使用Token缓存，剩余时间: 1654.7秒
[INFO] 缓存Token仍然有效
[INFO] Token缓存过期或无效，重新获取Token
[INFO] Token缓存已更新
```

## 测试方法

### 1. 运行测试脚本
```bash
python test_cache.py
```

### 2. 手动测试
```python
from lambda_function import random_proxy, getTheToken, get_cache_status

# 测试代理缓存
proxy1 = random_proxy()  # 第一次调用，从API获取
proxy2 = random_proxy()  # 第二次调用，使用缓存

# 测试Token缓存
token1 = getTheToken()   # 第一次调用，从API获取
token2 = getTheToken()   # 第二次调用，使用缓存

# 查看缓存状态
status = get_cache_status()
print(status)
```

## 注意事项

### 1. Lambda 冷启动
- Lambda 冷启动时缓存为空，需要重新获取
- 建议在高频使用时段保持 Lambda 容器温热

### 2. 内存使用
- 缓存数据存储在内存中，占用很少的内存空间
- 代理列表通常占用 < 1KB
- Token 通常占用 < 1KB

### 3. 错误处理
- 如果API请求失败，系统会尝试使用过期缓存
- 如果没有可用缓存，会返回相应的错误信息

### 4. 缓存一致性
- 每个 Lambda 容器实例有独立的缓存
- 多个并发请求可能会有短暂的缓存不一致

## 监控建议

1. **定期检查缓存状态**: 使用 `/cache-status` 接口监控缓存使用情况
2. **观察日志输出**: 通过 CloudWatch 日志监控缓存命中率
3. **性能监控**: 对比启用缓存前后的响应时间
4. **错误监控**: 关注缓存相关的错误日志

## 故障排除

### 缓存不工作
1. 检查 Lambda 函数是否重新部署
2. 查看日志确认缓存逻辑是否正常执行
3. 使用 `/clear-cache` 接口清理缓存后重试

### 性能没有提升
1. 确认是否在缓存有效期内进行测试
2. 检查网络延迟是否影响了基准测试
3. 确认 Lambda 容器是否被复用

### Token 频繁失效
1. 检查 Token 验证逻辑是否正常
2. 确认上游 Token 服务是否稳定
3. 考虑调整 Token 缓存时间
