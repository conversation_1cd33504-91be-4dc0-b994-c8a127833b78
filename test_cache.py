#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存功能测试脚本
用于测试 random_proxy 和 getTheToken 函数的缓存机制
"""

import sys
import time
import json
from lambda_function import random_proxy, getTheToken, get_cache_status, clear_cache

def test_proxy_cache():
    """测试代理缓存功能"""
    print("=" * 50)
    print("测试代理缓存功能")
    print("=" * 50)
    
    # 清理缓存
    clear_cache()
    print("1. 缓存已清理")
    
    # 第一次调用 - 应该从API获取
    print("\n2. 第一次调用 random_proxy():")
    start_time = time.time()
    proxy1 = random_proxy()
    time1 = time.time() - start_time
    print(f"   耗时: {time1:.2f}秒")
    print(f"   结果: {proxy1}")
    
    # 第二次调用 - 应该使用缓存
    print("\n3. 第二次调用 random_proxy() (应该使用缓存):")
    start_time = time.time()
    proxy2 = random_proxy()
    time2 = time.time() - start_time
    print(f"   耗时: {time2:.2f}秒")
    print(f"   结果: {proxy2}")
    
    # 显示缓存状态
    print("\n4. 缓存状态:")
    status = get_cache_status()
    print(f"   代理缓存有效: {status['proxy_cache']['valid']}")
    print(f"   剩余时间: {status['proxy_cache']['remaining_seconds']:.1f}秒")
    print(f"   代理数量: {status['proxy_cache']['data_count']}")
    
    print(f"\n5. 性能对比:")
    print(f"   第一次调用: {time1:.2f}秒")
    print(f"   第二次调用: {time2:.2f}秒")
    if time1 > 0:
        improvement = ((time1 - time2) / time1) * 100
        print(f"   性能提升: {improvement:.1f}%")

def test_token_cache():
    """测试Token缓存功能"""
    print("\n" + "=" * 50)
    print("测试Token缓存功能")
    print("=" * 50)
    
    # 清理缓存
    clear_cache()
    print("1. 缓存已清理")
    
    try:
        # 第一次调用 - 应该从API获取
        print("\n2. 第一次调用 getTheToken():")
        start_time = time.time()
        token1 = getTheToken()
        time1 = time.time() - start_time
        print(f"   耗时: {time1:.2f}秒")
        print(f"   Token: {token1[:20]}..." if token1 else "   Token: None")
        
        # 第二次调用 - 应该使用缓存
        print("\n3. 第二次调用 getTheToken() (应该使用缓存):")
        start_time = time.time()
        token2 = getTheToken()
        time2 = time.time() - start_time
        print(f"   耗时: {time2:.2f}秒")
        print(f"   Token: {token2[:20]}..." if token2 else "   Token: None")
        
        # 显示缓存状态
        print("\n4. 缓存状态:")
        status = get_cache_status()
        print(f"   Token缓存有效: {status['token_cache']['valid']}")
        print(f"   剩余时间: {status['token_cache']['remaining_seconds']:.1f}秒")
        print(f"   有Token: {status['token_cache']['has_token']}")
        
        print(f"\n5. 性能对比:")
        print(f"   第一次调用: {time1:.2f}秒")
        print(f"   第二次调用: {time2:.2f}秒")
        if time1 > 0:
            improvement = ((time1 - time2) / time1) * 100
            print(f"   性能提升: {improvement:.1f}%")
            
    except Exception as e:
        print(f"   Token测试失败: {e}")
        print("   这可能是因为网络问题或API不可用")

def test_cache_status():
    """测试缓存状态查看功能"""
    print("\n" + "=" * 50)
    print("测试缓存状态查看功能")
    print("=" * 50)
    
    status = get_cache_status()
    print("缓存状态详情:")
    print(json.dumps(status, indent=2, ensure_ascii=False))

def main():
    """主测试函数"""
    print("AWS Lambda 缓存功能测试")
    print("测试 random_proxy 和 getTheToken 函数的缓存机制")
    
    # 测试代理缓存
    test_proxy_cache()
    
    # 测试Token缓存
    test_token_cache()
    
    # 测试缓存状态
    test_cache_status()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
